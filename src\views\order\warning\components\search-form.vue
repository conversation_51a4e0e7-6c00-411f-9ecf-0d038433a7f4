<template>
    <div class="search-form">
        <m-card v-model="isShowForm">
            <el-form :model="searchData" label-width="90px">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="订单号">
                            <el-input v-model="searchData.orderNo" placeholder="请输入订单号" maxlength="50" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="供应商名称">
                            <el-input v-model="searchData.supplierName" placeholder="请输入供应商名称" maxlength="50" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="商品名称">
                            <el-input v-model="searchData.productName" placeholder="请输入商品名称" maxlength="50" clearable />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="处理人">
                            <el-input v-model="searchData.dealName" placeholder="请输入处理人" maxlength="50" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="预警类型">
                            <el-select v-model="searchData.noticeType" placeholder="请选择预警类型" clearable>
                                <el-option label="全部未处理" value="" />
                                <el-option label="发货超时" value="PENDING_SHIPMENT" />
                                <el-option label="揽收异常" value="PENDING_PICKUP" />
                                <el-option label="已处理" value="PROCESSED" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="售后状态">
                            <el-select v-model="searchData.afsStatus" placeholder="请选择售后状态" clearable>
                                <el-option label="全部" value="" />
                                <el-option label="不处于售后状态" value="NONE" />
                                <el-option label="退款申请" value="REFUND_REQUEST" />
                                <el-option label="退款已同意" value="REFUND_AGREE" />
                                <el-option label="退款已拒绝" value="REFUND_REJECT" />
                                <el-option label="已退款" value="REFUNDED" />
                                <el-option label="退货退款申请" value="RETURN_REFUND_REQUEST" />
                                <el-option label="退货退款已同意" value="RETURN_REFUND_AGREE" />
                                <el-option label="退货退款已拒绝" value="RETURN_REFUND_REJECT" />
                                <el-option label="已退货退款 已完成" value="RETURNED_REFUNDED" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="成交时间">
                            <el-date-picker
                                v-model="searchData.orderTimeRange"
                                type="daterange"
                                format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="发货时间">
                            <el-date-picker
                                v-model="searchData.deliveryTimeRange"
                                type="daterange"
                                format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="预警时间">
                            <el-date-picker
                                v-model="searchData.noticeTimeRange"
                                type="daterange"
                                format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="处理时间">
                            <el-date-picker
                                v-model="searchData.dealTimeRange"
                                type="daterange"
                                format="YYYY/MM/DD"
                                value-format="YYYY-MM-DD"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 操作按钮 -->
                <el-row style="margin-left: 30px">
                    <el-col :span="24">
                        <div class="actions">
                            <div class="actions__btns">
                                <el-button type="primary" @click="handleSearch">
                                    <el-icon><Search /></el-icon>
                                    搜索
                                </el-button>
                                <el-button @click="handleReset">
                                    <el-icon><Refresh /></el-icon>
                                    重置
                                </el-button>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </el-form>
        </m-card>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import MCard from '@/components/MCard.vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import type { SearchParams } from '@/apis/logistics/types'

// 定义 emits
const emit = defineEmits({
    search: (params: SearchParams) => true,
    'show-change': (visible: boolean) => true,
})

// 响应式数据
const isShowForm = ref(false)

// 搜索表单数据
const searchData = reactive({
    orderNo: '',
    supplierName: '',
    productName: '',
    dealName: '',
    noticeType: '',
    afsStatus: '',
    orderTimeRange: [] as string[],
    deliveryTimeRange: [] as string[],
    noticeTimeRange: [] as string[],
    dealTimeRange: [] as string[],
})

// 初始搜索数据（用于重置）
const initialSearchData = {
    orderNo: '',
    supplierName: '',
    productName: '',
    dealName: '',
    noticeType: '',
    afsStatus: '',
    orderTimeRange: [] as string[],
    deliveryTimeRange: [] as string[],
    noticeTimeRange: [] as string[],
    dealTimeRange: [] as string[],
}

// 监听表单显示状态变化
watch(
    () => isShowForm.value,
    (visible) => {
        emit('show-change', visible)
    },
)

// 处理搜索
const handleSearch = () => {
    const params: SearchParams = {
        orderNo: searchData.orderNo || undefined,
        supplierName: searchData.supplierName || undefined,
        productName: searchData.productName || undefined,
        dealName: searchData.dealName || undefined,
        noticeType: searchData.noticeType || undefined,
        afsStatus: searchData.afsStatus || undefined,
        orderTimeStart: searchData.orderTimeRange?.[0] || undefined,
        orderTimeEnd: searchData.orderTimeRange?.[1] || undefined,
        deliveryTimeStart: searchData.deliveryTimeRange?.[0] || undefined,
        deliveryTimeEnd: searchData.deliveryTimeRange?.[1] || undefined,
        noticeTimeStart: searchData.noticeTimeRange?.[0] || undefined,
        noticeTimeEnd: searchData.noticeTimeRange?.[1] || undefined,
        dealTimeStart: searchData.dealTimeRange?.[0] || undefined,
        dealTimeEnd: searchData.dealTimeRange?.[1] || undefined,
    }

    // 过滤掉空值
    const filteredParams = Object.fromEntries(Object.entries(params).filter(([_, value]) => value !== undefined && value !== ''))

    emit('search', filteredParams)
}

// 处理重置
const handleReset = () => {
    Object.assign(searchData, initialSearchData)
    emit('search', {})
}
</script>

<style lang="scss" scoped>
.search-form {
    background: #f9f9f9;
    margin-bottom: 20px;
}

.actions {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;

    &__btns {
        display: flex;
        gap: 12px;
    }
}

:deep(.el-form-item) {
    margin-bottom: 20px;
}

:deep(.el-input) {
    width: 100%;
}

:deep(.el-select) {
    width: 100%;
}

// 批量操作按钮禁用状态样式
:deep(.el-button.is-disabled) {
    opacity: 0.5;
    cursor: not-allowed;
}
</style>
